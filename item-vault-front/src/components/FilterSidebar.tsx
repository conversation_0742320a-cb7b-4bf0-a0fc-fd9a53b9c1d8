
import React from 'react';
import { Filter } from 'lucide-react';

interface FilterSidebarProps {
  categories: string[];
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  priceRange: { min: number; max: number };
  onPriceRangeChange: (range: { min: number; max: number }) => void;
  maxPrice: number;
}

const FilterSidebar: React.FC<FilterSidebarProps> = ({
  categories,
  selectedCategory,
  onCategoryChange,
  priceRange,
  onPriceRangeChange,
  maxPrice,
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex items-center mb-6">
        <Filter className="w-5 h-5 mr-2 text-gray-700" />
        <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
      </div>

      {/* Category Filter */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Category</h3>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="radio"
              name="category"
              value="all"
              checked={selectedCategory === 'all'}
              onChange={(e) => onCategoryChange(e.target.value)}
              className="w-4 h-4 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">All Categories</span>
          </label>
          {categories.map((category) => (
            <label key={category} className="flex items-center">
              <input
                type="radio"
                name="category"
                value={category}
                checked={selectedCategory === category}
                onChange={(e) => onCategoryChange(e.target.value)}
                className="w-4 h-4 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700 capitalize">
                {category.replace(/'/g, '')}
              </span>
            </label>
          ))}
        </div>
      </div>

      {/* Price Range Filter */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Price Range</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-xs text-gray-600 mb-1">Min Price</label>
            <input
              type="range"
              min="0"
              max={maxPrice}
              step="1"
              value={priceRange.min}
              onChange={(e) =>
                onPriceRangeChange({
                  ...priceRange,
                  min: Number(e.target.value),
                })
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <span className="text-sm text-gray-700">${priceRange.min.toFixed(2)}</span>
          </div>
          
          <div>
            <label className="block text-xs text-gray-600 mb-1">Max Price</label>
            <input
              type="range"
              min="0"
              max={maxPrice}
              step="1"
              value={priceRange.max}
              onChange={(e) =>
                onPriceRangeChange({
                  ...priceRange,
                  max: Number(e.target.value),
                })
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <span className="text-sm text-gray-700">${priceRange.max.toFixed(2)}</span>
          </div>
        </div>
      </div>

      {/* Clear Filters */}
      <button
        onClick={() => {
          onCategoryChange('all');
          onPriceRangeChange({ min: 0, max: maxPrice });
        }}
        className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-lg transition-colors duration-200 text-sm font-medium"
      >
        Clear All Filters
      </button>
    </div>
  );
};

export default FilterSidebar;
